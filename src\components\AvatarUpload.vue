<template>
  <div class="avatar-upload">
    <div class="avatar-preview" @click="triggerFileInput">
      <img 
        :src="previewUrl || getUserAvatarUrl()" 
        :alt="user?.username + '的头像'"
        class="avatar-img"
        @error="handleAvatarError"
      />
      <div class="avatar-overlay">
        <i class="upload-icon">📷</i>
        <span>点击上传</span>
      </div>
    </div>
    
    <input 
      ref="fileInput"
      type="file" 
      accept="image/png,image/jpg,image/jpeg,image/gif,image/webp"
      @change="handleFileSelect"
      style="display: none;"
    />
    
    <div class="avatar-actions">
      <button 
        @click="triggerFileInput" 
        class="btn btn-primary"
        :disabled="uploading"
      >
        {{ uploading ? '上传中...' : '选择头像' }}
      </button>
      
      <button 
        @click="deleteAvatar" 
        class="btn btn-danger"
        :disabled="uploading || !user?.avatar"
        v-if="user?.avatar"
      >
        删除头像
      </button>
    </div>
    
    <div class="upload-tips">
      <p>支持 PNG、JPG、JPEG、GIF、WEBP 格式</p>
      <p>文件大小不超过 5MB</p>
    </div>
    
    <!-- 错误提示 -->
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>
    
    <!-- 成功提示 -->
    <div v-if="successMessage" class="success-message">
      {{ successMessage }}
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { authAPI, authUtils } from '../utils/api';

// Props
const props = defineProps({
  user: {
    type: Object,
    default: null
  }
});

// Emits
const emit = defineEmits(['avatar-updated']);

// 响应式数据
const fileInput = ref(null);
const previewUrl = ref('');
const uploading = ref(false);
const errorMessage = ref('');
const successMessage = ref('');

// 获取用户头像URL
const getUserAvatarUrl = () => {
  if (props.user && props.user.avatar) {
    return authAPI.getAvatarUrl(props.user.avatar);
  }
  return new URL('../assets/avatar/default-avatar.svg', import.meta.url).href;
};

// 头像加载错误处理
const handleAvatarError = (event) => {
  event.target.src = new URL('../assets/avatar/default-avatar.svg', import.meta.url).href;
};

// 触发文件选择
const triggerFileInput = () => {
  fileInput.value?.click();
};

// 显示消息
const showMessage = (message, type = 'success') => {
  if (type === 'success') {
    successMessage.value = message;
    errorMessage.value = '';
  } else {
    errorMessage.value = message;
    successMessage.value = '';
  }
  
  setTimeout(() => {
    successMessage.value = '';
    errorMessage.value = '';
  }, 3000);
};

// 文件选择处理
const handleFileSelect = async (event) => {
  const file = event.target.files[0];
  if (!file) return;
  
  // 文件类型检查
  const allowedTypes = ['image/png', 'image/jpg', 'image/jpeg', 'image/gif', 'image/webp'];
  if (!allowedTypes.includes(file.type)) {
    showMessage('不支持的文件格式，请上传 PNG、JPG、JPEG、GIF 或 WEBP 格式的图片', 'error');
    return;
  }
  
  // 文件大小检查 (5MB)
  if (file.size > 5 * 1024 * 1024) {
    showMessage('文件大小不能超过5MB', 'error');
    return;
  }
  
  // 创建预览
  const reader = new FileReader();
  reader.onload = (e) => {
    previewUrl.value = e.target.result;
  };
  reader.readAsDataURL(file);
  
  // 上传文件
  await uploadFile(file);
};

// 上传文件
const uploadFile = async (file) => {
  uploading.value = true;
  
  try {
    const formData = new FormData();
    formData.append('avatar', file);
    
    const response = await authAPI.uploadAvatar(formData);
    const { avatar, message } = response.data;
    
    // 更新用户信息
    const currentUser = authUtils.getCurrentUser();
    if (currentUser) {
      currentUser.avatar = avatar;
      authUtils.setAuth(authUtils.getToken(), currentUser);
    }
    
    showMessage(message);
    emit('avatar-updated', avatar);
    
    // 清除预览
    previewUrl.value = '';
    
  } catch (error) {
    const errorMsg = error.response?.data?.error || '头像上传失败，请重试';
    showMessage(errorMsg, 'error');
    previewUrl.value = ''; // 清除预览
  } finally {
    uploading.value = false;
    // 清空文件输入
    if (fileInput.value) {
      fileInput.value.value = '';
    }
  }
};

// 删除头像
const deleteAvatar = async () => {
  if (!confirm('确定要删除头像吗？')) return;
  
  uploading.value = true;
  
  try {
    const response = await authAPI.deleteAvatar();
    const { message } = response.data;
    
    // 更新用户信息
    const currentUser = authUtils.getCurrentUser();
    if (currentUser) {
      currentUser.avatar = null;
      authUtils.setAuth(authUtils.getToken(), currentUser);
    }
    
    showMessage(message);
    emit('avatar-updated', null);
    
  } catch (error) {
    const errorMsg = error.response?.data?.error || '头像删除失败，请重试';
    showMessage(errorMsg, 'error');
  } finally {
    uploading.value = false;
  }
};
</script>

<style scoped>
.avatar-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 20px;
}

.avatar-preview {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  border: 3px solid #e5e7eb;
  transition: all 0.3s ease;
}

.avatar-preview:hover {
  border-color: #667eea;
  transform: scale(1.05);
}

.avatar-preview:hover .avatar-overlay {
  opacity: 1;
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  color: white;
  font-size: 12px;
}

.upload-icon {
  font-size: 24px;
  margin-bottom: 5px;
}

.avatar-actions {
  display: flex;
  gap: 10px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #5a67d8;
}

.btn-danger {
  background: #ef4444;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #dc2626;
}

.upload-tips {
  text-align: center;
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
}

.error-message {
  color: #ef4444;
  background: #fef2f2;
  padding: 10px 15px;
  border-radius: 6px;
  border: 1px solid #fecaca;
  font-size: 14px;
  text-align: center;
}

.success-message {
  color: #10b981;
  background: #f0fdf4;
  padding: 10px 15px;
  border-radius: 6px;
  border: 1px solid #bbf7d0;
  font-size: 14px;
  text-align: center;
}
</style>
