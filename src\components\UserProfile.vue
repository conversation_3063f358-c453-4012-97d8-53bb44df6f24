<template>
  <div class="user-profile">
    <div class="profile-container" v-if="!loading">
      <div class="profile-header">
        <h2>个人设置</h2>
              
      <div class="profile-actions">
        <button @click="goBack" class="btn btn-secondary">
          返回首页
        </button>
      </div>
        <p>管理您的个人信息和头像</p>
      </div>
      
      <div class="profile-content">
        <!-- 左侧头像区域 -->
        <div class="avatar-section">
          <div class="avatar-container">
            <AvatarUpload
              :user="user"
              @avatar-updated="handleAvatarUpdate"
            />
          </div>
        </div>

        <!-- 右侧基本信息区域 -->
        <div class="info-section">
          <div class="section-header">
            <div class="section-icon">ℹ️</div>
            <h3>基本信息</h3>
          </div>
          <div class="info-grid">
            <div class="info-item">
              <div class="info-icon">👤</div>
              <div class="info-content">
                <label>用户名</label>
                <div class="info-value">{{ user?.username || '未设置' }}</div>
              </div>
            </div>
            <div class="info-item">
              <div class="info-icon">📧</div>
              <div class="info-content">
                <label>邮箱</label>
                <div class="info-value">{{ user?.email || '未设置' }}</div>
              </div>
            </div>
            <div class="info-item">
              <div class="info-icon">📅</div>
              <div class="info-content">
                <label>注册时间</label>
                <div class="info-value">{{ formatDate(user?.created_at) || '未知' }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div class="loading-container" v-if="loading">
      <div class="loading-spinner"></div>
      <p>加载中...</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { authAPI, authUtils } from '../utils/api';
import AvatarUpload from './AvatarUpload.vue';

const router = useRouter();
const user = ref(null);
const loading = ref(true);

// 获取用户信息
const fetchUserProfile = async () => {
  loading.value = true;
  try {
    const response = await authAPI.getUserProfile();
    user.value = response.data.user;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    // 如果获取失败，使用本地存储的用户信息
    user.value = authUtils.getCurrentUser();
  } finally {
    loading.value = false;
  }
};

// 处理头像更新
const handleAvatarUpdate = (newAvatar) => {
  if (user.value) {
    user.value.avatar = newAvatar;
  }
  
  // 触发全局状态更新
  window.dispatchEvent(new Event('authStateChanged'));
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch (error) {
    return '日期格式错误';
  }
};

// 返回首页
const goBack = () => {
  router.push('/home');
};

// 组件挂载时获取用户信息
onMounted(() => {
  fetchUserProfile();
});
</script>

<style scoped>
.user-profile {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.user-profile::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(102, 126, 234, 0.2) 0%, transparent 50%);
  animation: backgroundFloat 20s ease-in-out infinite;
}

@keyframes backgroundFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(1deg); }
}

.user-profile {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.profile-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 50px;
  max-width: 700px;
  width: 100%;
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.12),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 1;
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.profile-header {
  text-align: center;
  margin-bottom: 50px;
  position: relative;
}

.profile-header::after {
  content: '';
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 2px;
}

.profile-header h2 {
  color: #2d3748;
  font-size: 2.5rem;
  margin-bottom: 12px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.profile-header p {
  color: #718096;
  font-size: 1.1rem;
  font-weight: 400;
}

.profile-content {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 50px;
  align-items: start;
}

.avatar-section {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.4));
  border-radius: 20px;
  padding: 30px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  transition: all 0.3s ease;
  height: fit-content;
}

.avatar-section:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.avatar-container::after {
  content: '';
  position: absolute;
  top: 50%;
  right: -25px;
  width: 1px;
  height: 60%;
  background: linear-gradient(to bottom, transparent, rgba(102, 126, 234, 0.3), transparent);
  transform: translateY(-50%);
}

.info-section {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.4));
  border-radius: 20px;
  padding: 40px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  transition: all 0.3s ease;
  height: fit-content;
}

.info-section:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 30px;
}

.section-header .section-icon {
  font-size: 1.8rem;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
}

.section-header h3 {
  color: #2d3748;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  position: relative;
}

.section-header h3::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 40px;
  height: 2px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 1px;
}

.info-grid {
  display: grid;
  gap: 25px;
}

.info-item {
  background: rgba(255, 255, 255, 0.6);
  border-radius: 16px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.4);
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.info-item:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.info-icon {
  font-size: 1.2rem;
  margin-top: 2px;
  opacity: 0.8;
}

.info-content {
  flex: 1;
}

.info-content label {
  font-weight: 600;
  color: #4a5568;
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 8px;
  display: block;
}

.info-content .info-value {
  color: #2d3748;
  font-size: 1.1rem;
  font-weight: 500;
  line-height: 1.5;
}

.profile-actions {
  margin-top: 40px;
  text-align: center;
}

.btn {
  padding: 14px 32px;
  border: none;
  border-radius: 50px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-secondary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-secondary:active {
  transform: translateY(0);
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  color: white;
  text-align: center;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container p {
  font-size: 1.1rem;
  font-weight: 500;
  opacity: 0.9;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .profile-content {
    grid-template-columns: 280px 1fr;
    gap: 40px;
  }
}

@media (max-width: 768px) {
  .user-profile {
    padding: 15px;
  }

  .profile-container {
    padding: 30px 25px;
    margin: 10px;
    border-radius: 20px;
  }

  .profile-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .profile-header h2 {
    font-size: 2rem;
  }

  .profile-header {
    margin-bottom: 35px;
  }

  .avatar-section {
    padding: 25px 20px;
    order: 1;
  }

  .avatar-container::after {
    display: none;
  }

  .info-section {
    padding: 25px 20px;
    order: 2;
  }

  .btn {
    padding: 12px 28px;
    font-size: 0.95rem;
  }
}

@media (max-width: 480px) {
  .profile-container {
    padding: 25px 20px;
  }

  .profile-header h2 {
    font-size: 1.8rem;
  }

  .avatar-section,
  .info-section {
    padding: 20px 15px;
  }

  .info-item {
    padding: 15px;
  }

  .section-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 10px;
  }

  .section-header h3::after {
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>
