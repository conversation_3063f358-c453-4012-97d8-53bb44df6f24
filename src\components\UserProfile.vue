<template>
  <div class="user-profile">
    <div class="profile-container">
      <div class="profile-header">
        <h2>个人设置</h2>
        <p>管理您的个人信息和头像</p>
      </div>
      
      <div class="profile-content">
        <!-- 头像设置区域 -->
        <div class="avatar-section">
          <h3>头像设置</h3>
          <AvatarUpload 
            :user="user" 
            @avatar-updated="handleAvatarUpdate"
          />
        </div>
        
        <!-- 基本信息区域 -->
        <div class="info-section">
          <h3>基本信息</h3>
          <div class="info-grid">
            <div class="info-item">
              <label>用户名</label>
              <div class="info-value">{{ user?.username || '未设置' }}</div>
            </div>
            <div class="info-item">
              <label>邮箱</label>
              <div class="info-value">{{ user?.email || '未设置' }}</div>
            </div>
            <div class="info-item">
              <label>注册时间</label>
              <div class="info-value">{{ formatDate(user?.created_at) || '未知' }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="profile-actions">
        <button @click="goBack" class="btn btn-secondary">
          返回首页
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { authAPI, authUtils } from '../utils/api';
import AvatarUpload from './AvatarUpload.vue';

const router = useRouter();
const user = ref(null);

// 获取用户信息
const fetchUserProfile = async () => {
  try {
    const response = await authAPI.getUserProfile();
    user.value = response.data.user;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    // 如果获取失败，使用本地存储的用户信息
    user.value = authUtils.getCurrentUser();
  }
};

// 处理头像更新
const handleAvatarUpdate = (newAvatar) => {
  if (user.value) {
    user.value.avatar = newAvatar;
  }
  
  // 触发全局状态更新
  window.dispatchEvent(new Event('authStateChanged'));
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch (error) {
    return '日期格式错误';
  }
};

// 返回首页
const goBack = () => {
  router.push('/home');
};

// 组件挂载时获取用户信息
onMounted(() => {
  fetchUserProfile();
});
</script>

<style scoped>
.user-profile {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.profile-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  max-width: 600px;
  width: 100%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.profile-header {
  text-align: center;
  margin-bottom: 40px;
}

.profile-header h2 {
  color: #333;
  font-size: 2rem;
  margin-bottom: 10px;
  font-weight: 600;
}

.profile-header p {
  color: #666;
  font-size: 1rem;
}

.profile-content {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.avatar-section,
.info-section {
  background: rgba(255, 255, 255, 0.5);
  border-radius: 15px;
  padding: 30px;
}

.avatar-section h3,
.info-section h3 {
  color: #333;
  font-size: 1.3rem;
  margin-bottom: 20px;
  font-weight: 600;
  text-align: center;
}

.info-grid {
  display: grid;
  gap: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item label {
  font-weight: 600;
  color: #555;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value {
  background: rgba(255, 255, 255, 0.8);
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  color: #333;
  font-size: 1rem;
}

.profile-actions {
  margin-top: 30px;
  text-align: center;
}

.btn {
  padding: 12px 30px;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.btn-secondary {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border: 2px solid #667eea;
}

.btn-secondary:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-profile {
    padding: 10px;
  }
  
  .profile-container {
    padding: 20px;
    margin: 10px;
  }
  
  .profile-header h2 {
    font-size: 1.5rem;
  }
  
  .avatar-section,
  .info-section {
    padding: 20px;
  }
}
</style>
