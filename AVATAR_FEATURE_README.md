# 用户头像功能说明

## 功能概述

本次更新为系统添加了完整的用户头像功能，包括头像上传、显示、更换和删除等功能。

## 主要功能

### 1. 数据库更新
- 在 `user` 表中添加了 `avatar` 字段，用于存储头像文件路径
- 提供了数据库更新脚本 `backend/update_db_avatar.sql`

### 2. 后端API接口
- **头像上传**: `POST /api/user/avatar`
  - 支持 PNG、JPG、JPEG、GIF、WEBP 格式
  - 文件大小限制：5MB
  - 自动调整图片大小为 300x300 像素
  - 生成唯一文件名防止冲突

- **头像获取**: `GET /api/user/avatar/<filename>`
  - 直接返回头像文件

- **头像删除**: `DELETE /api/user/avatar`
  - 删除用户头像文件和数据库记录

### 3. 前端功能
- **头像显示**: 在顶部导航栏显示用户头像
- **默认头像**: 未设置头像时显示默认头像
- **头像上传组件**: 支持拖拽上传和点击上传
- **个人设置页面**: 完整的用户信息管理界面

## 使用方法

### 数据库更新
1. 运行数据库更新脚本：
```sql
-- 在MySQL中执行
source backend/update_db_avatar.sql;
```

### 安装依赖
1. 后端依赖更新：
```bash
cd backend
pip install -r requirements.txt
```

### 使用头像功能
1. **查看头像**: 登录后在顶部导航栏可以看到头像
2. **设置头像**: 
   - 点击头像或"个人设置"按钮
   - 在个人设置页面上传头像
   - 支持预览和实时更新
3. **更换头像**: 在个人设置页面重新上传新头像
4. **删除头像**: 在个人设置页面点击"删除头像"按钮

## 技术特性

### 安全性
- 文件类型验证
- 文件大小限制
- 安全的文件名生成
- 用户权限验证

### 性能优化
- 图片自动压缩和调整大小
- 旧头像文件自动清理
- 前端图片懒加载

### 用户体验
- 实时预览
- 错误提示
- 加载状态显示
- 响应式设计

## 文件结构

```
backend/
├── conn.py                 # 主要API接口文件（已更新）
├── requirements.txt        # Python依赖（已更新）
├── update_db_avatar.sql    # 数据库更新脚本（新增）
└── uploads/
    └── avatars/           # 头像存储目录（新增）

src/
├── assets/
│   └── avatar/
│       └── default-avatar.svg  # 默认头像（新增）
├── components/
│   ├── AvatarUpload.vue        # 头像上传组件（新增）
│   └── UserProfile.vue         # 用户设置页面（新增）
├── utils/
│   └── api.js                  # API工具（已更新）
├── router/
│   └── index.js                # 路由配置（已更新）
└── App.vue                     # 主应用（已更新）
```

## 注意事项

1. 确保 `backend/uploads/avatars/` 目录有写入权限
2. 头像文件会自动压缩，建议上传高质量图片
3. 删除用户时需要同时清理对应的头像文件
4. 生产环境建议配置CDN或对象存储服务

## 后续扩展建议

1. 添加头像裁剪功能
2. 支持从第三方平台导入头像
3. 添加头像审核机制
4. 支持多种尺寸的头像生成
5. 集成图片CDN服务
