#!/usr/bin/env python3
"""
JWT问题修复脚本
"""

import subprocess
import sys

def run_command(command):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def fix_jwt():
    """修复JWT问题"""
    print("🔧 修复JWT问题...")
    
    # 1. 卸载可能冲突的jwt包
    print("1. 卸载冲突的jwt包...")
    success, stdout, stderr = run_command("pip uninstall jwt -y")
    if success:
        print("✅ 卸载jwt包成功")
    else:
        print("⚠️  jwt包可能未安装")
    
    # 2. 卸载PyJWT
    print("2. 卸载PyJWT...")
    success, stdout, stderr = run_command("pip uninstall PyJWT -y")
    if success:
        print("✅ 卸载PyJWT成功")
    else:
        print("⚠️  PyJWT可能未安装")
    
    # 3. 重新安装PyJWT
    print("3. 重新安装PyJWT...")
    success, stdout, stderr = run_command("pip install PyJWT==2.4.0")
    if success:
        print("✅ 安装PyJWT成功")
    else:
        print(f"❌ 安装PyJWT失败: {stderr}")
        return False
    
    # 4. 测试JWT功能
    print("4. 测试JWT功能...")
    try:
        import jwt
        import datetime
        
        # 测试编码
        payload = {'test': 'data', 'exp': datetime.datetime.utcnow() + datetime.timedelta(hours=1)}
        token = jwt.encode(payload, 'secret', algorithm='HS256')
        
        # 确保token是字符串
        if isinstance(token, bytes):
            token = token.decode('utf-8')
        
        # 测试解码
        decoded = jwt.decode(token, 'secret', algorithms=['HS256'])
        
        print("✅ JWT功能测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ JWT功能测试失败: {e}")
        return False

if __name__ == "__main__":
    if fix_jwt():
        print("\n🎉 JWT问题修复完成!")
        print("现在可以重新启动Flask服务器了")
    else:
        print("\n❌ JWT问题修复失败")
        print("请手动执行以下命令:")
        print("pip uninstall jwt PyJWT -y")
        print("pip install PyJWT==2.4.0")
